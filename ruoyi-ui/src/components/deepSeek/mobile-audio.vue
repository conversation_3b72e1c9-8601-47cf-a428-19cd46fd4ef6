<template>
  <div
    class="mobile-audio-container"
    :class="{
      'mobile-recording': isRecording
    }"
    :style="{
      'border-top-left-radius': showExcelHelper && excelBox ? 0 : '15px',
      'border-top-right-radius': showExcelHelper && excelBox ? 0 : '15px'
    }"
  >
    <!-- <div v-show="showExcelHelper" class="excel-help" @click="exitExcel">
      excel助手
    </div> -->
    <div class="mobile-audio-input">
      <base-input
        v-if="!speechMode && list && list.length && textarea"
        :value.sync="textarea"
        :placeholder="!showExcelHelper ? '有问题,尽管问...' : '请输入对于上传文件的需求'"
        :list="list"
        class="base-textarea"
        @input="handleInput"
      />
      <el-input
        ref="textareaRef"
        v-show="!speechMode && !list"
        v-model="textarea"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 4 }"
        :placeholder="!showExcelHelper ? '有问题,尽管问...' : '请输入对于上传文件的需求'"
        size="large"
        @input="handleInput"
      />
      <div
        v-show="speechMode"
        ref="press"
        class="mobile-audio-btn"
        @touchstart="touchOnSpeech"
        @touchend="touchOffSpeech"
        @touchmove="touchMoveSpeech"
      >
        <span v-show="!isRecording">按住说话</span>
        <div v-show="isRecording" class="recording-icon">
          <audioWave />
        </div>
      </div>
    </div>
    <span class="mobile-audio-icon" @click="changeMode">
      <svg-icon :icon-class="!speechMode ? 'audio' : 'keyboard'" />
    </span>
  </div>
</template>
<script>
import { transcriptions } from '@/api/deepSeekApi'
import audioWave from './audio-wave.vue'
import baseInput from './base-input.vue'

export default {
  name: 'MobileAudio',
  components: {
    audioWave,
    baseInput
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    list: {
      type: [Array, String],
      default: ''
    },
    showExcelHelper: {
      type: Boolean,
      default: false
    },
    excelBox: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      textarea: '',
      speechMode: false,
      isRecording: false,
      mediaRecorder: null,
      status: '', // 判断当前处于录音什么状态
      startTime: 0
    }
  },
  watch: {
    value() {
      this.initVal()
    }
  },
  mounted() {
    this.initVal()
  },
  methods: {
    /** 发送消息 */
    sendClick() {
      this.$emit('send')
    },
    handleInput() {
      this.$emit('input', this.textarea)
    },
    initVal() {
      this.textarea = this.value
    },
    initData() {
      this.isRecording = false
    },
    async initMedia() {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        try {
          this.isRecording = true
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true
          })
          this.mediaRecorder = new MediaRecorder(stream)
          const audioChunks = []
          this.mediaRecorder.ondataavailable = (e) => {
            audioChunks.push(e.data)
          }
          this.mediaRecorder.onstop = () => {
            console.log('Recording stopped')
            if (this.status === 'cancel') {
              this.initData()
              return
            }
            const audioBlob = new Blob(audioChunks, { type: 'audio/mpeg' })
            this.uploadAudio(audioBlob)
            // const audioURL = URL.createObjectURL(audioBlob)
            this.initData()
          }
          this.mediaRecorder.start()
        } catch (error) {
          console.error('Error accessing the microphone', error)
          if (error.name === 'NotAllowedError') {
            this.$message.closeAll()
            this.$message.warning({ message: '请打开录音权限', duration: 3000 })
            // this.$message.warning('请打开录音权限')
          }
          this.initData()
        }
      } else {
        this.$message.closeAll()
        this.$message.warning({ message: '您的浏览器不支持录音功能，请升级浏览器', duration: 3000 })
        // this.$message.warning('您的浏览器不支持录音功能，请升级浏览器')
        this.initData()
      }
    },
    changeMode() {
      this.speechMode = !this.speechMode
    },
    async touchOnSpeech() {
      if (this.isRecording) {
        return
      }
      this.initMedia()
      this.startTime = Date.now()
      this.status = 'record'
    },
    async touchOffSpeech() {
      const endTime = Date.now()
      const duration = endTime - this.startTime
      console.log('duration-end', duration, this.isRecording)
      if (this.isRecording) {
        if (duration < 1000) {
          this.$message.closeAll()
          this.$message.warning({ message: '请说话超过1秒', duration: 3000 })
          // this.$message.warning('请说话超过1秒')
        } else {
          if (['record'].includes(this.status)) {
            this.mediaRecorder.stop()
          }
        }
      }
      this.mediaRecorder = null
      this.initData()
    },
    handleClick() {
      this.touchOffSpeech()
    },
    /** 判断触摸点是否在录音按钮上 */
    touchMoveSpeech({ touches }) {
      console.log(touches)
      const touche = touches[0]
      const position = this.$refs.press.getBoundingClientRect()
      if (touche.pageY < position.top) {
        this.status = 'cancel'
      } else {
        this.status = 'record'
      }
    },
    uploadAudio(audioBlob) {
      console.log('uploading audio', audioBlob)
      const formData = new FormData()
      formData.append('file', audioBlob, '1.mp3')
      transcriptions(formData)
        .then((response) => {
          this.textarea = response.text
          this.speechMode = false
          this.$emit('input', this.textarea)
          this.$emit('transAudioSuccess', response.text)
        })
        .catch((error) => {
          this.$message.closeAll()
          this.$message.warning({ message: error.response.data.message, duration: 3000 })
          // this.$message.warning(error.response.data.message)
        })
    },
    // 退出excel模式
    exitExcel() {
      this.$emit('exitExcel')
    },
    focusInput() {
      this.$refs.textareaRef.focus()
    }
  }
}
</script>
<style lang="scss" scoped>
$radius: 15px;
.mobile-audio-container {
  display: flex;
  width: 100%;
  align-items: center;
  background: #fff;
  color: #727272;
  // border-radius: $radius;
  .mobile-audio-input {
    flex: 1;
    ::v-deep .el-textarea__inner {
      // background: #f5f5f5;
      border: none;
      border-top-left-radius: $radius;
      border-bottom-left-radius: $radius;
      min-height: 36px !important;
    }
    .mobile-audio-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      user-select: none;
      height: 36px;
      .recording-icon {
        width: 100%;
        margin-left: 30px;
      }
    }
  }
  .mobile-audio-icon {
    color: #090909;
    font-size: 24px;
    padding: 0 10px;
  }
}
.mobile-recording {
  background: var(--color-primary-light);
  color: (var(--color-primary));
}
.excel-help {
  padding-left: 12px;
  color: #4cb848;
}
</style>

<style>
textarea::-webkit-resizer {
  display: none; /* 隐藏右下角调整大小把手 */
}
</style>
