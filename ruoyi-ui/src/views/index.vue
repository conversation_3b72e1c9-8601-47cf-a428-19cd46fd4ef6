<template>
  <div class="deeepseek-index">
    <div class="indexPage">
      <div class="inner h100">
        <el-row class="flex-container h100">
          <transition name="slide-fade">
            <el-col :style="{ width: isCollapse ? '260px' : '85px' }" class="leftSide h100">
              <div default-active="1-4-1" class="el-menu-vertical-container padding-20">
                <div
                  v-if="isCollapse"
                  class="margin-top-8 flex flex-between"
                  style="margin-bottom: 8px"
                >
                  <el-button
                    class="chat-btn"
                    type="primary"
                    round
                    plain
                    style="padding: 10px 42px; border-radius: 10px"
                    @click="newChatClick"
                  >
                    <div class="flex">
                      <svg-icon icon-class="chat1" class="chat1" />
                      <span>开启新对话</span>
                    </div>
                  </el-button>
                  <!-- <el-button
                  v-if="historyList && historyList.length > 0"
                  class="clearAll"
                  plain
                  circle
                  type='text'
                  @click="delAllClick"
                >
                  <svg-icon icon-class="clear-all" />
                </el-button> -->
                  <el-tooltip class="item" effect="dark" content="收起" placement="top-start">
                    <span>
                      <svg-icon
                        icon-class="expand "
                        class="expand-top collapse-expand pointer margin-left-10"
                        @click="isCollapse = !isCollapse"
                      />
                    </span>
                  </el-tooltip>
                </div>
                <!--              对话列表-->
                <div v-if="isCollapse" class="chat-list-container">
                  <div class="mask-left" />
                  <div v-for="(item, i) of historyTypeList" :key="i">
                    <div v-if="item.children.length">
                      <div class="chat-title">{{ item.title }}</div>
                      <div
                        v-for="v in item.children"
                        :key="v.chatId"
                        class="chat-list-item msgItem flex flex-between"
                        :class="{
                          'self-blue msgItem-active': curChat.chatId == v.chatId
                        }"
                      >
                        <div class="ellipsis chat-item-title" @click="handleHistoryItemClick(v)">
                          <el-tooltip
                            v-if="isTitleOverflow(v)"
                            class="item"
                            effect="dark"
                            :content="v.customTitle || v.title"
                            placement="top"
                          >
                            <div :ref="'titleRef_' + v.chatId" class="ellipsis chat-item-content">
                              {{ v.customTitle || v.title }}
                            </div>
                          </el-tooltip>
                          <div
                            v-else
                            :ref="'titleRef_' + v.chatId"
                            class="ellipsis chat-item-content"
                          >
                            {{ v.customTitle || v.title }}
                          </div>
                        </div>

                        <el-dropdown trigger="click" class="chat-item-dropdown">
                          <i class="el-icon-more" />
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click="toTopClick(v)">
                              <span
                                class="top-action"
                                style="display: inline-block; width: 100%"
                                @click="toTopClick(v)"
                              >
                                <svg-icon class="margin-right-5" icon-class="to-top" />
                                {{ v.timeCategory === 'top' ? '取消置顶' : '置顶' }}
                              </span>
                            </el-dropdown-item>
                            <el-dropdown-item>
                              <span
                                class="top-action"
                                style="display: inline-block; width: 100%"
                                @click="editTitleClick(v)"
                              >
                                <svg-icon class="margin-right-5" icon-class="custom-title" />
                                自定义标题
                              </span>
                            </el-dropdown-item>
                            <el-dropdown-item>
                              <span
                                class="top-action"
                                style="display: inline-block; width: 100%"
                                @click="delHistoryClick(v)"
                              >
                                <svg-icon class="margin-right-5" icon-class="delete" />
                                删除
                              </span>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                  <div class="mask-left-b" />
                </div>
                <div v-if="!isCollapse">
                  <div class="flex flex-center">
                    <svg-icon icon-class="zdLogo" class="collapse-icon" />
                  </div>
                  <div class="flex flex-center">
                    <el-tooltip class="box-item" effect="dark" content="展开" placement="right">
                      <span>
                        <svg-icon
                          icon-class="expand"
                          class="pointer collapse-icon1"
                          @click="isCollapse = !isCollapse"
                        />
                      </span>
                    </el-tooltip>
                  </div>
                  <div class="flex flex-center">
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      content="开启新对话"
                      placement="right"
                    >
                      <span>
                        <svg-icon
                          icon-class="chat1"
                          class="collapse-icon pointer"
                          @click="newChatClick"
                        />
                      </span>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <div class="series-bottom">
                <div v-if="!isCollapse" class="flex flex-center series-bottom1">
                  <el-tooltip class="box-item" effect="dark" content="意见反馈" placement="right">
                    <span @click="openFeedbackDialog">
                      <svg-icon icon-class="feedback" class="collapse-icon2" />
                    </span>
                  </el-tooltip>
                </div>
                <!-- <div v-else class="service-info flex">
                <svg-icon icon-class="service" class="collapse-service" />
                <span>服务热线：010-66298666</span>
              </div> -->
                <!-- 左下积分功能 -->
                <div v-else class="service-info">
                  <el-divider class="service-divider" />
                  <div class="service-content">
                    <el-button
                      class="chat-btn margin-top-20"
                      type="primary"
                      round
                      plain
                      style="width: 245px; height: 50px; padding: 0; border-radius: 10px"
                      @click="openFeedbackDialog"
                    >
                      <div class="flex" style="justify-content: center; width: 100%">
                        <svg-icon icon-class="feedback" class="chat2" />
                        <span>意见反馈</span>
                      </div>
                    </el-button>
                  </div>
                  <div
                    class="user-name flex margin-top-34"
                    style="justify-content: flex-start; width: 100%; padding-left: 20px"
                  >
                    <el-dropdown trigger="click" placement="top" @command="handleUserCommand">
                      <el-button
                        type="text"
                        style="color: #343435; display: flex; align-items: center; padding: 0"
                      >
                        <div class="user-info-box">
                          <div class="user-avatar">
                            <div class="user-avatar-img" />
                            <!-- <i class="el-icon-user-solid" style="font-size: 40px; " /> -->
                          </div>
                          <div class="user-info-text">
                            <div class="user-info-row">
                              <span class="user-info-label">用户名：</span>
                              <span class="user-info-value">{{ userName }}</span>
                            </div>
                            <div class="user-info-row">
                              <span class="user-info-label1">总积分：</span>
                              <span class="user-info-value points-value">{{ points }}</span>
                            </div>
                          </div>
                        </div>
                      </el-button>
                      <el-dropdown-menu slot="dropdown" class="user-dropdown-menu">
                        <div class="user-dropdown-header">
                          <div class="user-avatar-large">
                            <i class="el-icon-user-solid" style="font-size: 20px" />
                          </div>
                          <div class="user-dropdown-name">用户名：{{ userName }}</div>
                        </div>
                        <el-divider style="margin-left: 12px" />
                        <el-dropdown-item command="getPoints" class="user-dropdown-item">
                          <svg-icon icon-class="points" class="dropdown-icon green-icon" />
                          <span>获取积分</span>
                        </el-dropdown-item>
                        <el-dropdown-item command="pointsRank" class="user-dropdown-item">
                          <svg-icon icon-class="rankpoints" class="dropdown-icon green-icon" />
                          <span>积分排行</span>
                        </el-dropdown-item>
                        <el-dropdown-item command="myfeedback" class="user-dropdown-item">
                          <svg-icon icon-class="feedbacklist" class="dropdown-icon green-icon" />
                          <span>我的反馈</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </el-col>
          </transition>
          <!--右边-->
          <el-col v-if="!showFirst" class="flex rightEa">
            <!-- <div class="collapse" @click="isCollapse = !isCollapse">
            <div class="arrowEa">
              <i v-if="isCollapse" class="el-icon-arrow-left"></i>
              <i v-else class="el-icon-arrow-right"></i>
            </div>
          </div> -->
            <div v-if="curChat" class="padding-10 flex flex-center chat-content-top">
              <span>{{ curChat.customTitle || curChat.title }}</span>
            </div>
            <!--          内容区-->
            <div
              v-if="curChat"
              id="contentBox"
              ref="contentEa"
              class="content"
              style="flex: 1"
              @scroll="handleScroll"
            >
              <div class="mask" />
              <div v-for="(v, i) in msgList" :key="v.dataId + i">
                <com-msg-left
                  v-if="v.obj === 'AI'"
                  :info="v"
                  :app-id="appId"
                  :llm-type="llm_type"
                  :chat-id="curChat.chatId"
                  @sendComMsgLeft="sendComMsgLeft"
                  @firstCharacterRendered="handleFirstCharacterRendered"
                  @typingAnimationStarted="handleTypingAnimationStarted"
                  @contentUpdated="handleContentUpdated"
                  @contentRenderComplete="handleContentRenderComplete"
                />
                <com-msg-right
                  v-if="v.obj === 'Human'"
                  :info="v"
                  :app-id="appId"
                  :chat-id="curChat.chatId"
                  @sendComMsgRightDel="sendComMsgRightDel"
                  @sendComMsgRightReSubmit="sendComMsgRightReSubmit"
                  @sendComMsgRight="sendComMsgRight"
                  @sendComMsgRightEdit="sendComMsgRightEdit"
                />
              </div>
              <div
                v-if="msgList.length == 0"
                class="empty flex"
                style="height: 100%; align-self: center; justify-content: center"
              >
                <static-content />
              </div>
              <!-- 返回底部按钮 -->
              <!-- <el-button
              v-show="showBackToBottom"
              class="back-to-bottom-btn"
              circle
              type="primary"
              icon="el-icon-arrow-down"
              @click="scrollToBottom('btnClick')"
            /> -->
            </div>
            <!-- 新增回到最新位置的按钮 -->
            <div v-if="!isAtBottom" class="back-to-bottom-container" style="position: relative">
              <el-button
                class="scroll-to-bottom-btn"
                circle
                plain
                @click="forceScrollToBottom"
              >
                <i class="el-icon-arrow-down" style="font-size: 20px" />
              </el-button>
            </div>
            <!-- <div class="mask-b"></div> -->
            <div class="inputEa" style="width: 100%">
              <div class="mask-b" />
              <com-file-upload-dialog
                v-if="isShowFunBox"
                ref="comFileUploadDialog"
                :fun-title="funTitle"
                @sendComFileUploadClose="sendComFileUploadClose"
                @sendComFileUploadDialog="sendComFileUploadDialog"
              />
              <div
                v-if="(!fileList || fileList.length == 0) && !isShowFunBox && !isShowExcelBox"
                class="margin-bottom-10"
              >
                <document-button
                  @docTranslateClick="docTranslateClick"
                  @docProofreadingClick="docProofreadingClick"
                  @docSummaryClick="docSummaryClick"
                  @docReadClick="docReadClick"
                  @assistantClick="assistantClick"
                  @excelAssistantClick="excelAssistantClick"
                />
              </div>
              <div v-if="isShowExcelBox && excelbox" class="excel margin-bottom-10">
                <el-row :gutter="20">
                  <el-col :span="4">
                    <el-card style="visibility: hidden" />
                  </el-col>
                  <el-col :span="8">
                    <el-card shadow="hover">
                      <div
                        class="excel-img1"
                        style="cursor: pointer"
                        @click.stop="sendComExcelAssistDialog(0)"
                      >
                        <svg-icon icon-class="excel1" class="excel-icon margin-right-8" />
                        <span
                          style="font-size: 14px; color: #898787"
                        >各分公司销售额-示例数据.xlsx</span>
                        <div class="excel-img">
                          <img
                            src="@/assets/images/excel-eg.png"
                            style="width: 100%; max-height: 200px"
                          >
                        </div>
                        <div class="card-desc-overlay">快速进行数据合并/拆分、去重等复杂操作。</div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card shadow="hover">
                      <div
                        class="excel-img1"
                        style="cursor: pointer"
                        @click="sendComExcelAssistDialog(1)"
                      >
                        <svg-icon icon-class="excel1" class="excel-icon margin-right-8" />
                        <span
                          style="font-size: 14px; color: #898787"
                        >商品销售情况_示例数据.xlsx</span>
                        <div class="excel-img">
                          <img
                            src="@/assets/images/excel-eg2.png"
                            style="width: 100%; max-height: 200px"
                          >
                        </div>
                        <div class="card-desc-overlay">
                          精准进行数据对比分析、统计分析、透视分析、相关分析。
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="4">
                    <el-card style="visibility: hidden" />
                  </el-col>
                  <!-- <el-col :span="8" shadow="hover">
                  <el-card>
                    <div class="excel-img1" style="cursor: pointer;" @click.stop="sendComExcelAssistDialog(2)">
                      <svg-icon icon-class="excel1" class="excel-icon margin-right-8" />
                      <span style="font-size: 14px;color: #898787;">人口统计_示例数据.xlsx</span>
                      <div class="excel-img">
                        <img
                          src="@/assets/images/excel-eg3.png"
                          style="width: 100%; max-height: 200px"
                        >
                      </div>
                      <div class="card-desc-overlay">
                        绘制柱状图、饼状图、折线图、流程图等各种可视化图表。
                      </div>
                    </div>
                  </el-card>
                </el-col> -->
                </el-row>
              </div>
              <div :class="{ 'input-container': true, inputActive: textarea.length > 0 }">
                <div class="fileEa flex">
                  <div
                    v-for="(v, index) in fileList"
                    :key="index"
                    class="margin-tb-10 margin-left-10 flex"
                  >
                    <!-- <div
                    class="imgType"
                    v-if="
                      imageFileExtensions.includes(v.raw.type.split('/')[1])
                    "
                  >
                  </div> -->
                    <div class="fileType">
                      <file-preview :file="v" :img-type="['jpg', 'png', 'jpeg']" />
                      <div class="el-icon-error closeBtn" @click="delFileClick(v, index)" />
                    </div>
                  </div>
                  <div
                    v-if="fileList && fileList.length > 0"
                    class="el-icon-plus addBtn"
                    @click="triggerUpload"
                  />
                </div>
                <!-- :class="{ textarea: true, inputActive: textarea.length > 0 }" -->
                <div class="textarea">
                  <!-- // 顶部 -->
                  <div v-if="!isExcel" class="textarea1">
                    <!-- <div
                    v-if="isShowExcelBox"
                    class=" excel"
                  >
                    <div class="excel-assist-tag">
                      <el-button
                        type="text"
                        class="close-btn"
                        @click="isShowExcelBox = false"
                      >
                        <svg-icon icon-class="excelclose" class="excel-icon" />
                        <span class="excel-text">excel助手</span>
                      </el-button>
                    </div>
                  </div> -->
                    <transition>
                      <base-input
                        v-if="textList && textList.length && textarea"
                        :value.sync="textarea"
                        :placeholder="placeholder"
                        :list="textList"
                        class="base-textarea"
                        @enter="handleBaseInputEnter"
                        @keyup.native="handleKeyup"
                        @paste.native="handlePaste"
                        @compositionstart.native="handleCompositionStart"
                        @compositionend.native="handleCompositionEnd"
                      />
                      <el-input
                        v-else
                        v-model="textarea"
                        style="position: relative"
                        type="textarea"
                        :placeholder="placeholder"
                        class="deep-input"
                        :autosize="{ minRows: 5, maxRows: 10 }"
                        @keydown.native="handleKeydown"
                        @keyup.native="handleKeyup"
                        @paste.native="handlePaste"
                        @compositionstart.native="handleCompositionStart"
                        @compositionend.native="handleCompositionEnd"
                      />
                    </transition>
                  </div>
                  <div v-else class="textarea1">
                    <div v-if="isShowExcelBox" class="excel">
                      <div class="excel-assist-tag">
                        <el-tooltip class="item" effect="dark" placement="top">
                          <div slot="content">
                            <span>点击退出功能</span>
                          </div>
                          <el-button
                            type="text"
                            class="close-btn"
                            @click="
                              isShowExcelBox = false
                              isExcel = true
                              funTitle = ''
                              excelbox = false
                            "
                          >
                            <svg-icon icon-class="excelclose" class="excel-icon" />
                            <span class="excel-text">excel助手</span>
                          </el-button>
                        </el-tooltip>
                      </div>
                    </div>
                    <transition>
                      <base-input
                        v-if="textList && textList.length && textarea"
                        :value.sync="textarea"
                        :placeholder="placeholder"
                        :list="textList"
                        class="base-textarea"
                        @enter="handleBaseInputEnter"
                        @keyup.native="handleKeyup"
                        @paste.native="handlePaste"
                        @compositionstart.native="handleCompositionStart"
                        @compositionend.native="handleCompositionEnd"
                      />
                      <el-input
                        v-else
                        v-model="textarea"
                        style="position: relative"
                        type="textarea"
                        :placeholder="placeholder"
                        class="deep-input"
                        :autosize="{ minRows: 5, maxRows: 10 }"
                        @keydown.native="handleKeydown"
                        @keyup.native="handleKeyup"
                        @paste.native="handlePaste"
                        @compositionstart.native="handleCompositionStart"
                        @compositionend.native="handleCompositionEnd"
                      />
                    </transition>
                  </div>
                  <!-- 底部 -->
                  <!-- 模型切换按钮 -->
                  <div class="modelSelEa flex flex-between">
                    <div class="flex">
                      <!-- <chat-model
                        v-model="llm_type"
                        :online.sync="online"
                        :knowledge-type.sync="knowledgeType"
                      /> -->
                      <chat-model v-model="llm_type" :fun-title="funTitle" :online.sync="online" :knowledge-type.sync="knowledgeCode" />
                      <!-- <el-popover
                      ref="popover"
                      placement="top"
                      width="200"
                      trigger="click"
                    >
                      <com-self-sel
                        v-model="llm_type"
                        :list="llmTypeList"
                        @change="popoverChange"
                      />
                      <el-button slot="reference" size="mini" round>
                        <svg-icon
                          class="margin-right-5"
                          icon-class="change-model"
                        />
                        {{ getLabel(llm_type, llmTypeList) }}
                      </el-button>
                    </el-popover> -->
                      <!-- <com-audio
                      style="margin-left: 8px"
                      @transAudioSuccess="transAudioSuccess"
                    /> -->
                      <computer-audio
                        style="margin-left: 10px"
                        @transAudioSuccess="transAudioSuccess"
                      />
                    </div>
                    <div class="text-center flex send-btns">
                      <div
                        v-if="funTitle === '中英互译' && showTranslateBar && false"
                        class="translate-bar flex-text"
                        style="margin-right: 16px"
                      >
                        <el-select
                          v-model="translateFrom"
                          size="mini"
                          style="width: 100px"
                          class="custom-select"
                          @change="handleDocument"
                        >
                          <el-option label="自动检测" value="自动检测" />
                          <el-option label="中文" value="中文" />
                          <el-option label="英文" value="英文" />
                          <!-- 可扩展更多语言 -->
                        </el-select>
                        <span style="margin: 0 4px; color: #a3a3a3">⇄</span>
                        <el-select
                          v-model="translateTo"
                          size="mini"
                          style="width: 70px"
                          class="custom-select"
                          @change="handleDocument"
                        >
                          <el-option label="中文" value="中文" />
                          <el-option label="英文" value="英文" />
                          <!-- 可扩展更多语言 -->
                        </el-select>
                      </div>
                      <!--      <el-button size="small" type="primary">点击上传</el-button>-->
                      <div slot="trigger" class="pointer mr-16">
                        <el-tooltip class="item" effect="dark" placement="top">
                          <div slot="content">
                            <span v-if="funTitle === 'excel助手'">文件格式:xls、xlsx</span>
                            <span
                              v-else
                            >文件格式:支持txt、doc、docx、ppt、pptx、pdf、xls、xlsx</span>
                            <br
                              v-if="
                                funTitle !== 'excel助手' &&
                                  funTitle !== '文档校对' &&
                                  funTitle !== '文档总结'
                              "
                            >
                            <span
                              v-if="
                                funTitle !== 'excel助手' &&
                                  funTitle !== '文档校对' &&
                                  funTitle !== '文档总结'
                              "
                            >图片格式:支持jpg、png、jpeg
                            </span>
                            <br>
                            <span>文件大小:不能超过 50 MB</span>
                          </div>
                          <span>
                            <svg-icon
                              icon-class="upload4"
                              class="upload-icon"
                              @click.native="handleClickUpload"
                            />
                          </span>
                        </el-tooltip>
                      </div>
                      <div
                        v-if="showHelpRead && startReading"
                        class="pointer stopReadBtn"
                        @click="handleStopReading"
                      >
                        <el-tooltip effect="dark" content="停止助读" placement="top">
                          <audio-wave :num="4" />
                        </el-tooltip>
                      </div>
                      <div class="pointer sendBtn ml-16">
                        <el-tooltip
                          v-if="isThinking"
                          effect="dark"
                          content="停止思考"
                          placement="top"
                        >
                          <svg-icon
                            :icon-class="'thinking'"
                            class="active-btn"
                            @click="sendClick('stop')"
                          />
                        </el-tooltip>

                        <svg-icon
                          v-else-if="!textarea && fileList.length == 0"
                          icon-class="sendMsg-new"
                          class="disabled-btn"
                        />
                        <svg-icon
                          v-else
                          :icon-class="'sendMsg-new'"
                          class="active-btn"
                          @click="sendClick()"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col v-else class="flex rightEa no-history-list">
            <static-content />

            <div class="inputEa" style="width: 100%">
              <com-file-upload-dialog
                v-if="isShowFunBox"
                ref="comFileUploadDialog"
                :fun-title="funTitle"
                @sendComFileUploadClose="sendComFileUploadClose"
                @sendComFileUploadDialog="sendComFileUploadDialog"
              />
              <div :class="{ 'input-container': true, inputActive: textarea.length > 0 }">
                <div class="fileEa flex">
                  <div
                    v-for="(v, index) in fileList"
                    :key="index"
                    class="margin-tb-10 margin-left-10 flex"
                  >
                    <div class="fileType">
                      <file-preview :file="v" :img-type="['jpg', 'png', 'bmp', 'jpeg']" />
                      <div class="el-icon-error closeBtn" @click="delFileClick(v, index)" />
                    </div>
                  </div>
                  <div
                    v-if="fileList && fileList.length > 0"
                    class="el-icon-plus addBtn"
                    @click="triggerUpload"
                  />
                </div>
                <!-- :class="{ textarea: true, inputActive: textarea.length > 0 }" -->
                <div class="textarea">
                  <div v-if="isShowExcelBox" class="excel">
                    <div class="excel-assist-tag">
                      <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">
                          <span>点击退出功能</span>
                        </div>
                        <el-button
                          type="text"
                          class="close-btn"
                          @click="
                            isShowExcelBox = false
                            funTitle = ''
                          "
                        >
                          <svg-icon icon-class="excelclose" class="excel-icon" />
                          <span class="excel-text">excel助手</span>
                        </el-button>
                      </el-tooltip>
                    </div>
                  </div>
                  <transition>
                    <base-input
                      v-if="textList && textList.length && textarea"
                      :value.sync="textarea"
                      :placeholder="placeholder"
                      :list="textList"
                      class="base-textarea"
                      @enter="handleBaseInputEnter"
                      @keyup.native="handleKeyup"
                      @paste.native="handlePaste"
                      @compositionstart.native="handleCompositionStart"
                      @compositionend.native="handleCompositionEnd"
                    />
                    <el-input
                      v-else
                      v-model="textarea"
                      style="position: relative"
                      type="textarea"
                      :placeholder="placeholder"
                      class="deep-input"
                      :autosize="{ minRows: 5, maxRows: 10 }"
                      @keydown.native="handleKeydown"
                      @keyup.native="handleKeyup"
                      @paste.native="handlePaste"
                      @compositionstart.native="handleCompositionStart"
                      @compositionend.native="handleCompositionEnd"
                    />
                  </transition>
                  <!-- 模型切换按钮 -->
                  <div class="modelSelEa flex flex-between">
                    <div class="flex">
                      <!-- <el-popover
                      ref="popover"
                      placement="top"
                      width="200"
                      trigger="click"
                    >
                      <com-self-sel
                        v-model="llm_type"
                        :list="llmTypeList"
                        @change="popoverChange"
                      />
                      <el-button slot="reference" size="mini" round>
                        <svg-icon
                          class="margin-right-5"
                          icon-class="change-model"
                        />
                        {{ getLabel(llm_type, llmTypeList) }}
                      </el-button>
                    </el-popover> -->
                      <!-- <chat-model
                        v-model="llm_type"
                        :online.sync="online"
                        :knowledge-type.sync="knowledgeType"
                      /> -->
                      <chat-model v-model="llm_type" :fun-title="funTitle" :online.sync="online" :knowledge-type.sync="knowledgeCode" />
                      <!-- <com-audio
                      style="margin-left: 8px"
                      @transAudioSuccess="transAudioSuccess"
                    /> -->
                      <computer-audio
                        style="margin-left: 10px"
                        @transAudioSuccess="transAudioSuccess"
                      />
                    </div>
                    <div class="text-center flex send-btns">
                      <!--      <el-button size="small" type="primary">点击上传</el-button>-->
                      <div
                        v-if="textarea.includes(['中英互译']) && showTranslateBar && false"
                        class="translate-bar flex-text"
                        style="margin-right: 16px"
                      >
                        <el-select
                          v-model="translateFrom"
                          size="mini"
                          style="width: 100px"
                          class="custom-select"
                          @change="handleDocument"
                        >
                          <el-option label="自动检测" value="自动检测" />
                          <el-option label="中文" value="中文" />
                          <el-option label="英文" value="英文" />
                          <!-- 可扩展更多语言 -->
                        </el-select>
                        <span style="margin: 0 4px; color: #a3a3a3">⇄</span>
                        <el-select
                          v-model="translateTo"
                          size="mini"
                          style="width: 70px"
                          class="custom-select"
                          @change="handleDocument"
                        >
                          <el-option label="中文" value="中文" />
                          <el-option label="英文" value="英文" />
                          <!-- 可扩展更多语言 -->
                        </el-select>
                      </div>
                      <div slot="trigger" class="pointer mr-16 margin-left 16px">
                        <el-tooltip class="item" effect="dark" placement="top">
                          <div slot="content">
                            <span v-if="funTitle === 'excel助手'">文件格式:xls、xlsx</span>
                            <span
                              v-else
                            >文件格式:支持txt、doc、docx、ppt、pptx、pdf、xls、xlsx</span>
                            <br
                              v-if="
                                funTitle !== 'excel助手' &&
                                  funTitle !== '文档校对' &&
                                  funTitle !== '文档总结'
                              "
                            >
                            <span
                              v-if="
                                funTitle !== 'excel助手' &&
                                  funTitle !== '文档校对' &&
                                  funTitle !== '文档总结'
                              "
                            >图片格式:支持jpg、png、jpeg
                            </span>
                            <br>
                            <span>文件大小:不能超过 50 MB</span>
                          </div>
                          <span>
                            <svg-icon
                              icon-class="upload4"
                              class="upload-icon"
                              @click.native="handleClickUpload"
                            />
                          </span>
                        </el-tooltip>
                      </div>
                      <div class="pointer sendBtn">
                        <el-tooltip
                          v-if="isThinking"
                          class="item"
                          effect="dark"
                          content="停止思考"
                          placement="top"
                        >
                          <svg-icon
                            :icon-class="'thinking'"
                            class="active-btn"
                            @click="sendClick('stop')"
                          />
                        </el-tooltip>
                        <svg-icon
                          v-else-if="!textarea && fileList.length == 0"
                          icon-class="sendMsg-new"
                          class="disabled-btn"
                        />
                        <svg-icon
                          v-else
                          :icon-class="'sendMsg-new'"
                          class="active-btn"
                          @click="sendClick()"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="(!fileList || fileList.length == 0) && !isShowFunBox && !isShowExcelBox"
                class="margin-top-20"
                style="margin-top: 14px"
              >
                <document-button
                  @docTranslateClick="docTranslateClick"
                  @docProofreadingClick="docProofreadingClick"
                  @docSummaryClick="docSummaryClick"
                  @docReadClick="docReadClick"
                  @assistantClick="assistantClick"
                  @excelAssistantClick="excelAssistantClick"
                />
              </div>
              <div v-if="isShowExcelBox" class="excel margin-top-10">
                <el-row :gutter="20">
                  <el-col :span="4">
                    <el-card style="visibility: hidden" />
                  </el-col>
                  <el-col :span="8">
                    <el-card shadow="hover">
                      <div
                        class="excel-img1"
                        style="cursor: pointer"
                        @click.stop="sendComExcelAssistDialog(0)"
                      >
                        <svg-icon icon-class="excel1" class="excel-icon margin-right-8" />
                        <span
                          style="font-size: 14px; color: #898787"
                        >各分公司销售额-示例数据.xlsx</span>
                        <div class="excel-img">
                          <img
                            src="@/assets/images/excel-eg.png"
                            style="width: 100%; max-height: 200px"
                          >
                        </div>
                        <div class="card-desc-overlay">快速进行数据合并/拆分、去重等复杂操作。</div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card shadow="hover">
                      <div
                        class="excel-img1"
                        style="cursor: pointer"
                        @click.stop="sendComExcelAssistDialog(1)"
                      >
                        <svg-icon icon-class="excel1" class="excel-icon margin-right-8" />
                        <span
                          style="font-size: 14px; color: #898787"
                        >商品销售情况_示例数据.xlsx</span>
                        <div class="excel-img">
                          <img
                            src="@/assets/images/excel-eg2.png"
                            style="width: 100%; max-height: 200px"
                          >
                        </div>
                        <div class="card-desc-overlay">
                          精准进行数据对比分析、统计分析、透视分析、相关分析。
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="4">
                    <el-card style="visibility: hidden" />
                  </el-col>
                  <!-- <el-col :span="8" shadow="hover">
                  <el-card>
                    <div class="excel-img1" style="cursor: pointer;" @click.stop="sendComExcelAssistDialog(2)">
                      <svg-icon icon-class="excel1" class="excel-icon margin-right-8" />
                      <span style="font-size: 14px;color: #898787;">人口统计_示例数据.xlsx</span>
                      <div class="excel-img">
                        <img
                          src="@/assets/images/excel-eg3.png"
                          style="width: 100%; max-height: 200px"
                        >
                      </div>
                      <div class="card-desc-overlay">
                        绘制柱状图、饼状图、折线图、流程图等各种可视化图表。
                      </div>
                    </div>
                  </el-card>
                </el-col> -->
                </el-row>
              </div>
            </div>
            <!-- <div class="bottom-tips flex">
            <span>内容由AI生成，仅供参考</span>
          </div> -->
            <div class="service-hotline-footer flex">
              <div class="bottom-tips flex">
                <span>内容由AI生成，仅供参考</span>
              </div>
              <svg-icon icon-class="service1" class="service-icon" />
              <span>服务热线：010-66298888转3</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <com-title-edit ref="comTitleEdit" :info="curTitle" @sendComTitleEdit="init()" />
      <com-assistance ref="comAssistance" :info="curChat" @sendComAssistance="sendComAssistance" @comAssistanceClose="comAssistanceClose" />

      <el-upload
        ref="fileUpload"
        class="upload-demo"
        :action="baseUrl + '/api/common/file/upload'"
        :on-change="handleFileChange"
        :on-exceed="handleExceed"
        :on-error="handleUploadError"
        :before-upload="beforeAvatarUpload"
        multiple
        auto-upload
        :on-progress="onProgress"
        :data="uploadData"
        :limit="8"
        :show-file-list="false"
        :on-success="handleUploadSuccess"
        :file-list="fileList"
      />
      <com-feedback-dialog ref="comFeedbackDialog" />
      <pointRank ref="pointRank" />
      <pointRule ref="pointRule" />
      <myFeedback ref="myFeedback" />
    </div>
  </div>
</template>

<script>
import ComFeedbackDialog from '@/components/deepSeek/comFeedbackDialog.vue'
// import ComMsgLeft from '@/components/deepSeek/com-msg-left.vue'
import ComMsgLeft from '@/components/deepSeek/com-msg-left_renderChar.vue'
import ComMsgRight from '@/components/deepSeek/com-msg-right.vue'
import ComTitleEdit from '@/components/deepSeek/com-title-edit.vue'
// import ComAudio from '@/components/deepSeek/comAudio.vue'
import staticContent from '@/components/deepSeek/static-content.vue'
import documentButton from '../components/deepSeek/document-button.vue'
import pointRule from '@/components/elDialog/pointRule.vue'
import pointRank from '@/components/elDialog/pointRank.vue'
import myFeedback from '../components/elDialog/myFeedback.vue'
import { getSysuser, userbehavior } from '@/api/points'
import {
  chatInit1,
  clearHistories,
  delHistory,
  getHistories,
  getInitData,
  getPaginationRecords,
  itemDelete,
  updateHistory,
  getSignature,
  getAgentTicket,
  getAddEncryptUserInfo,
  getUser,
  uploadFile
} from '@/api/deepSeekApi'
// import TextMsgClass from '@/views/TextMsgClass'
// import TextCompletions from '@/views/TextCompletions'
import FileCompletions from '@/views/FileCompletions'
import FileMsgClass from '@/views/FileMsgClass'
import { generateChatId } from '@/utils/deepseek'
import ComAssistance from '@/components/deepSeek/comAssistance.vue'
import ComFileUploadDialog from '@/components/deepSeek/comFileUploadDialog.vue'
// import ComFileUploadDialog from '@/components/deepSeek/comFileUploadDialog_copy.vue'
// import ComSelfSel from '@/components/deepSeek/com-self-sel.vue'
import { FILE_TYPE, MSG_TYPE } from '../utils/constant'
import computerAudio from '@/components/deepSeek/computerAudio.vue'
import ComputerAudio from '../components/deepSeek/computerAudio.vue'
import BaseInput from '@/components/deepSeek/base-input.vue'
import ChatModel from '@/components/deepSeek/chat-model.vue'
import AudioWave from '@/components/deepSeek/audio-wave.vue'
import useSse from './useSse'
import ComExcelAssistDialog from '../components/deepSeek/com.vue'
import { eventBus } from '@/utils/eventBus'

const imageFileExtensions = [
  'jpg',
  'jpeg',
  'png',
  'gif',
  'bmp',
  'svg',
  'webp',
  'tiff',
  'tif',
  'psd',
  'raw',
  'heic',
  'heif',
  'avif',
  'ico',
  'cur',
  'apng',
  'pjpeg',
  'pjp'
]
const appId =
  process.env.NODE_ENV === 'production' ? '67a86833ee92f8ff6e2ccebe' : '67c811aa870793870db6010a'
const baseUrl = process.env.VUE_APP_BASE_API
// const appId = '67c819f9870793870db6302b'
// const appId = "67889533a13c9cb2f3b2a708"
export default {
  name: 'Index',
  components: {
    ComFeedbackDialog,
    // ComSelfSel,
    ComFileUploadDialog,
    ComAssistance,
    // ComAudio,
    ComTitleEdit,
    ComMsgRight,
    ComMsgLeft,
    staticContent,
    documentButton,
    computerAudio,
    ComputerAudio,
    BaseInput,
    ChatModel,
    AudioWave,
    pointRank,
    pointRule,
    myFeedback,
    ComExcelAssistDialog
  },
  mixins: [useSse],
  data() {
    return {
      // showBackToBottom: false,
      hasScrolled: false,
      showHelpRead: false, // 是否显示语音助读终止按钮
      startReading: false, // 开始语音助读
      online: 0, // 是否联网搜索
      knowledgeCode: '', // 知识库类型
      // 版本号
      textList: '',
      textarea: '',
      funTitle: '',
      isCollapse: true,
      imageFileExtensions,
      msgList: [],
      curChat: '',
      appId: appId,
      commonUrl: process.env.NODE_ENV === 'development' ? '/dev-api' : '',
      shareId: null,
      curTitle: '',
      fileList: [],
      preFileList: [],
      historyList: [],
      baseUrl: baseUrl,
      isThinking: false,
      autoScroll: true, // 是否自动滚动
      userScrolling: false, // 用户是否正在手动滚动
      jsScrolling: false, // 是否正在执行JS滚动
      isAtBottom: true,
      isShowFunBox: false,
      pageNum: 1,
      pageSize: 100,
      llm_type: 1,
      llmTypeList: [
        { label: 'DeepSeek-R1', value: 0 },
        { label: 'DeepSeek-V3', value: 1 }
      ],
      speechList: [],
      usedShortcutInCurSessionNumber: 0,
      historyTypeList: [],
      msgType: 'common',
      userName: '--',
      points: 0,
      showTranslateBar: true, // 控制翻译按钮显示
      translateFrom: '自动检测',
      translateTo: '英文',
      titleOverflowMap: {},
      isShowExcelBox: false,
      isExcel: false,
      excelbox: false,
      isComposition: false,
      compositionEndTimer: null
    }
  },
  computed: {
    showFirst() {
      if (this.curChat && this.curChat.chatId) {
        const chat = this.historyList.find((el) => el.chatId === this.curChat.chatId)
        // console.log(chat)
        return !chat
      }
      return true
    },
    // 动态计算上传数据，包含bizType
    uploadData() {
      // 根据funTitle设置msgType，就像sendClickContent中的逻辑一样
      let bizType = 'common'
      if (this.funTitle && MSG_TYPE[this.funTitle]) {
        bizType = MSG_TYPE[this.funTitle]
      }

      console.log('上传时的bizType:', bizType, 'funTitle:', this.funTitle)

      return {
        metadata: JSON.stringify({ chatId: this.curChat.chatId }),
        bucketName: 'chat',
        bizType: bizType,
        data: JSON.stringify({
          appId: this.appId
          // 传递业务类型到upload接口
        })
      }
    },
    placeholder() {
      //  当是excel助手时，显示专用文案  或者文件列表有东西的时候也显示
      if (this.isShowExcelBox) {
        return '请输入对于上传文件的需求，Shift+Enter可换行'
      }
      return this.fileList.length > 0
        ? '请输入对于上传文件的需求，Shift+Enter可换行'
        : '有问题，尽管问...按Shift+Enter换行'
    }
  },
  watch: {
    historyList: {
      handler(newVal, old) {
        // console.log('watch-historyList', newVal, old)
        this.pickerHistoryType()
      },
      deep: true
    },
    textarea: {
      handler(newVal) {
        if (!newVal) {
          // 只有当完全清空输入框时才重置状态
          this.textList = ''
          if (this.fileList.length === 0) {
            this.funTitle = ''
          }
        }
      }
    }
  },
  async mounted() {
    window?.speechSynthesis?.cancel()
    const shareId = localStorage.getItem('shareId')
    if (!shareId) {
      this.shareId =
        process.env.NODE_ENV === 'production'
          ? 'igalbyrcd9wrbpxmhyndaw1s'
          : '4lu1lad9sv3q7z86mn29cbw3'
      localStorage.setItem('shareId', '4lu1lad9sv3q7z86mn29cbw3')
    }
    // 初始化用户信息
    this.initUserInfo()

    const urlTest = new URLSearchParams(location.href)
    // console.log('当前获取到的 location.href', location.href)
    const isDev = ['development'].includes(process.env.VUE_APP_ENV)
    const isIncludesElink = String(urlTest).includes('elink')
    eventBus.$on('refreshPoints', () => {
      const staffId = localStorage.getItem('staffId')
      this.getUserTotalPoints({ staffId })
    })

    if (isIncludesElink || isDev) {
      // 有elink的情况
      if (String(urlTest).includes('elinkpoc') && window.self !== window.top) {
        // 判断url是否包含elinkpoc且 当前页面是否是在ifeame
        console.log('电投宜-AI工作台-内网测试')
        this.iframeMessage()
      } else {
        console.log('直接访问电投宜DeepSeek内网测试')
        this.initPage()
      }
    } else {
      // 内网情况下测试
      const _this = this
      console.log('单独AI工作台内网测试')
      this.iframeMessage()
      const urlParams = new URLSearchParams(window.location.search)
      // 获取特定参数值
      const token = urlParams.get('access_token')
      console.log(token, 'url参数', this.$route.query)
      if (token) {
        console.log('4A跳转到内网DeepSeek')
        localStorage.setItem('token', token)
        await this.queryUser()
        await this.initPage()
      }
    }

    // 测试代码
    const url = window.location.href.split('#')[0]
    await getSignature({ url })
      .then((getSign) => {
        // console.log('getSignature', getSign)
        if (!getSign) return
        const { corpId, timestamp, nonceStr, signature } = getSign
        window.wx.config({
          beta: true,
          debug: false,
          appId: corpId,
          timestamp,
          nonceStr,
          signature,
          jsApiList: [
            'hideOptionMenu',
            'chooseMessageFile',
            'getLocalFileData',
            'getCurUserExternalContact'
          ]
        })
        window.wx.ready(() => {
          getAgentTicket({ url }).then((getAgentSign) => {
            if (!getAgentSign) return
            const { timestamp, nonceStr, signature, agentId, corpId } = getAgentSign
            window.wx.agentConfig({
              agentid: agentId,
              corpid: corpId,
              timestamp,
              nonceStr,
              signature,
              jsApiList: ['chooseMessageFile', 'getLocalFileData', 'getCurUserExternalContact'],
              success: (res) => {
                window.wx.invoke('getCurExternalContact', {}, function(res) {
                  console.log('externalContact', res)
                  // if (res.err_msg == 'getCurExternalContact:ok') {
                  //   getCidByExtId(res.userId)
                  //   alreadyGet = true
                  // }
                })
              },
              fail: (res) => {}
            })
          })
        })
        window.wx.error((err) => {
          console.log(err)
        })
        window.wx.hideOptionMenu()
      })
      .catch((err) => {
        console.log(err)
      })
    this.$nextTick(() => {
      this.checkAllTitleOverflow()
    })
  },
  updated() {
    this.$nextTick(() => {
      this.checkAllTitleOverflow()
      this.checkIfAtBottom()
    })
  },
  beforeDestroy() {
    window?.speechSynthesis?.cancel()
    this.ctrl = null
    eventBus.$off('refreshPoints')
  },
  methods: {
    handleBaseInputEnter(e) {
      // 处理base-input组件的回车事件
      console.log('base-input 回车事件')
      this.sendClick()
    },
    async iframeMessage() {
      const _this = this
      window.addEventListener('message', async(event) => {
        console.log(process.env.VUE_APP_INNER_URL, '父传子Received message:', event)
        const userCode = event.data?.user?.userCode || ''
        console.log(userCode, 'PC端内网进入')
        if (event.data.type === process.env.VUE_APP_INNER_URL && userCode) {
          console.log('进入内网环境-3')
          await _this.queryUserInfo(userCode)
          await _this.initPage()
        }
      })
    },
    openFeedbackDialog() {
      this.$refs.comFeedbackDialog.show()
    },
    /**
     * 初始化用户姓名
     */
    initUserInfo() {
      console.log('initUserInfo')
      let staffId = ''
      // 如果当前为开发环境
      if (['development'].includes(process.env.VUE_APP_ENV)) {
        staffId = process.env.VUE_APP_DEV_STAFF_ID
      } else {
        // 测试和正式环境
        staffId = localStorage.getItem('staffId')
      }
      this.getUserName({ staffId: staffId })
      this.getUserTotalPoints(staffId)
    },
    getUserName(staffId) {
      getSysuser(staffId).then((res) => {
        if (res && res.code === 200 && res.data) {
          this.userName = res.data.userName || '--'
        }
      })
    },
    /**
     * 初始化用户总积分
     */
    getUserTotalPoints(staffId) {
      userbehavior(staffId).then((res) => {
        if (res && res.code === 200 && res.data) {
          this.points = res.data.points || ''
        }
      })
    },
    handleKeydown(e) {
      // console.log('handleKeydown', e.keyCode, 'isComposition:', this.isComposition)

      if (e.keyCode === 13 && !e.shiftKey) {
        // 检查是否处于输入法组合状态
        if (this.isComposition) {
          console.log('处于输入法组合状态，不触发回车事件')
          return
        }

        // 只有当输入框有内容时才发送消息
        if (this.textarea.trim() || (this.fileList && this.fileList.length > 0)) {
          e.preventDefault() // 阻止默认的换行行为
          console.log('发送消息')
          this.sendClick()
        }
      }
    },
    handleKeyup(e) {
      // 保留原有的处理逻辑，但不做发送操作
      // console.log('handleKeyup', e.keyCode, 'isComposition:', this.isComposition)
    },
    handleCompositionStart() {
      // 当开始输入法组合时，设置状态为 true
      // console.log('composition start')
      this.isComposition = true

      // 清除可能存在的定时器
      if (this.compositionEndTimer) {
        clearTimeout(this.compositionEndTimer)
        this.compositionEndTimer = null
      }
    },
    handleCompositionEnd() {
      // 当结束输入法组合时，设置状态为 false
      console.log('composition end')

      // 使用定时器延迟设置 isComposition 为 false
      // 这样可以确保在 keyup 事件之后再更新状态
      this.compositionEndTimer = setTimeout(() => {
        this.isComposition = false
        console.log('composition 状态延迟更新为 false')
      }, 100)
    },
    handleClickUpload() {
      // 只有不是中英互译时才清空
      // if (this.funTitle !== '中英互译') {
      //   this.funTitle = ''
      //   this.isShowFunBox = false
      // }
      this.showTranslateBar = true
      // this.textarea = ''
      this.$refs.fileUpload.$refs['upload-inner'].handleClick()
    },
    /** 获取内网用户信息 */
    async queryUser() {
      console.log('进入内容')
      const res = await getUser()
      if (res && res.code === 200) {
        const userCode = res?.data?.user?.userCode
        if (!userCode) {
          console.error('用户信息获取失败')
          return
        }
        await this.queryUserInfo(userCode)
      }
    },
    /** 获取内网用户加密信息 */
    async queryUserInfo(userCode) {
      const resData = await getAddEncryptUserInfo({
        userId: userCode
      })
      console.log(resData, '想要的用户')

      if (resData && resData.code === 200) {
        this.getUserName({ staffId: resData.staffId })
        localStorage.setItem('outLinkUid', resData.UserId)
        localStorage.setItem('staffId', resData.staffId)
      }
    },
    /** 初始化页面 */
    async initPage() {
      this.initCurrentChat()
      this.initAIMsg()
      getInitData({
        appId: this.appId
      }).then((res) => {})

      const res = await getUser()
      if (res && res.code === 200) {
        const userCode = res?.data?.user?.userCode
        const resData = await getAddEncryptUserInfo({
          userId: userCode
        })
        if (resData && resData.code === 200) {
          localStorage.setItem('outLinkUid', resData.UserId)
          localStorage.setItem('staffId', resData.staffId)
          getHistories({
            offset: 0,
            pageSize: this.pageSize,
            appId: appId,
            source: 'online'
          }).then(async(res) => {
            console.log('initPage')
            this.historyList = [...res.data.list]
          })
        }
      }

      // 每日登录积分任务 - 每天首次登录时调用
      this.handleDailyLoginPoints()
    },
    handleDailyLoginPoints() {
      const staffId = localStorage.getItem('staffId')
      if (!staffId) return
      const lastLoginDate = localStorage.getItem('lastLoginPointsDate')
      const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD
      if (lastLoginDate !== today) {
        this.getUserTotalPoints({ staffId })

        localStorage.setItem('lastLoginPointsDate', today)
      }
    },
    initCurrentChat(currentChat) {
      this.usedShortcutInCurSessionNumber = 0
      const chatId = generateChatId(24)
      this.curChat = currentChat || {
        chatId: chatId,
        title: '新的会话',
        appId: appId,
        top: false,
        timeCategory: 'today'
      }
      localStorage.setItem('curChat', JSON.stringify(this.curChat))
    },
    // 点击加号时的上传逻辑
    beforeAvatarUpload(file) {
      let fileType = []
      if (this.funTitle === '文档校对' || this.funTitle === '文档总结') {
        fileType = [...FILE_TYPE]
      } else if (this.funTitle === 'excel助手') {
        fileType = ['xlsx', 'xls']
      } else {
        fileType = [...FILE_TYPE, 'jpg', 'png', 'jpeg']
      }
      // const fileType = [...FILE_TYPE, 'jpg', 'png', 'jpeg']
      const list = file.name.split('.')
      // const fileSize = 10
      const fileSize = 50
      if (!fileType.includes(list[list.length - 1])) {
        // this.$message.error('上传文件格式不正确')
        this.$message.warning({ message: '上传文件格式不正确', duration: 3000 })
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        // this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        this.$message.warning({ message: `上传文件大小不能超过 ${fileSize}MB!`, duration: 3000 })
        return false
      }
      return isLt2M
    },
    /** 对会话列表分类 */
    pickerHistoryType() {
      this.historyTypeList = [
        {
          label: '置顶',
          value: 'top'
        },
        {
          label: '今日',
          value: 'today'
        },
        {
          label: '昨日',
          value: 'yesterday'
        },
        {
          label: '最近7天',
          value: 'last7Days'
        },
        {
          label: '历史',
          value: 'history'
        }
      ].map((item) => {
        return {
          title: item.label,
          children: this.historyList.filter((el) => el.timeCategory === item.value)
        }
      })
      // console.log(this.historyTypeList, 'historyTypeList')
    },
    addUsedShortcutInCurSessionNumber() {
      this.usedShortcutInCurSessionNumber = 0
      this.msgList.forEach((item) => {
        if (item.obj === 'Human' && item.value && item.value.length > 0) {
          item.value.forEach((element) => {
            if (element.type === 'text' && element.text && element.text.content) {
              const text = element.text.content.trim()
              const arr = [
                '中英互译',
                '文档校对',
                '文档总结',
                '文档阅读',
                '提示词帮助',
                'excel助手'
              ]
              if (arr.includes(text)) {
                this.usedShortcutInCurSessionNumber = this.usedShortcutInCurSessionNumber + 1
              }
            }
          })
        }
      })
    },
    handleStopReading() {
      this.showHelpRead = false
      this.closeSpeak()
    },
    /** 关闭语音助读 */
    closeSpeak() {
      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel() // 停止当前播放
      }
      this.speechList = []
    },
    /** 初始化AI消息状态 */
    initAIMsg() {
      this.isThinking = false
      this.showHelpRead = false
      // console.log('是不是这样处的问题')
      this.closeSpeak()
    },
    /**
     * 获取历史会话列表
     * @param pageNum  第几页
     */
    async getChatHistoryList(pageNum) {
      let list = []
      const res = await getHistories({
        offset: 0,
        pageSize: this.pageSize,
        appId: appId,
        source: 'online',
        pageNum
      })
      if (res && res.code === 200) {
        console.log('getChatHistoryList')
        this.historyList = [...res.data.list]
        list = [...res.data.list]
      }
      return list
    },
    async init() {
      // todo
      await getInitData({
        appId: this.appId
      }).then((res) => {})
      await getHistories({
        offset: 0,
        pageSize: this.pageSize,
        appId: appId,
        source: 'online'
      }).then(async(res) => {
        console.log('init')
        this.historyList = [...res.data.list]
        if (this.historyList.length) {
          let index = this.historyList.findIndex((v) => v.chatId === this.curChat.chatId)
          if (index === -1) {
            index = index === -1 ? 0 : index
            this.curChat = this.historyList[index]
            await this.historyItemClick(this.curChat)
          }

          this.scrollToBottom(1311)
        } else {
          this.msgList = []
          this.initCurrentChat()
        }

        // if (
        //   res.data.total == 0 ||
        //   this.historyList.findIndex(
        //     (v) => v.chatId === this.curChat.chatId
        //   ) === -1
        // ) {
        //   await this.newChatClick();
        // } else {
        //   await this.historyItemClick(this.curChat);
        //   this.scrollToBottom();
        // }
      })
    },
    assistantClick() {
      this.$refs.comAssistance.show()
      this.funTitle = '提示词帮助'
      this.msgType = 'common' // 重置msgType
      this.closeUpload()
    },
    docTranslateClick() {
      this.funTitle = '中英互译'
      this.msgType = MSG_TYPE[this.funTitle] || 'common' // 同时设置msgType
      this.textarea = this.funTitle
      this.textList = ''
      this.isShowFunBox = true
      this.showTranslateBar = true // 每次点击都显示
      this.isExcel = false
      // this.$refs.comFileUploadDialog.show()
    },
    docSummaryClick() {
      this.funTitle = '文档总结'
      this.msgType = MSG_TYPE[this.funTitle] || 'common' // 同时设置msgType
      this.textarea = this.funTitle
      this.textList = ''
      this.isShowFunBox = true
      this.isExcel = false
      // this.$refs.comFileUploadDialog.show()
    },
    docReadClick() {
      this.funTitle = '文档阅读'
      this.msgType = MSG_TYPE[this.funTitle] || 'common' // 同时设置msgType
      this.textarea = this.funTitle
      this.textList = ''
      this.isShowFunBox = true
      this.isExcel = false
    },
    docProofreadingClick() {
      this.funTitle = '文档校对'
      this.msgType = MSG_TYPE[this.funTitle] || 'common' // 同时设置msgType
      this.textarea = this.funTitle
      this.textList = ''
      this.isShowFunBox = true
      this.isExcel = false
    },
    // 添加Excel助手相关方法
    excelAssistantClick() {
      this.isShowExcelBox = true
      this.funTitle = 'excel助手'
      // this.textarea = '请输入对于上传数据的分析和处理需求'
      this.msgType = MSG_TYPE[this.funTitle] || 'common'
      // this.msgType = 'common'
      this.textList = ''
      // console.log('打开Excel助手')
      this.isExcel = true
      if (this.msgList.length == 0) {
        this.excelbox = true
      }
    },
    async sendComExcelAssistDialog(index) {
      console.log('Excel助手选择:', index)
      let file = ''
      let text = ''
      switch (index) {
        case 0:
          // 各分公司销售额_示例数据.xlsx
          // 帮我把文件中的所有sheet按相同列名合并到一张表中，重新生成一个excel文件
          file = './template/各分公司销售额_示例数据.xlsx'
          text = '帮我把文件中的所有sheet按相同列名合并到一张表中'
          break
        case 1:
          file = './template/商品销售情况_示例数据.xlsx'
          text = '告诉我不同品类的下单次数和销售额是多少'
          // 商品销售情况_示例数据.xlsx
          // 告诉我不同品类的总下单次数和总销售额是多少
          break
        case 2:
          file = './template/人口统计_示例数据.xlsx'
          text = '统计20-29岁、30-39岁、40-49岁的女性人数占比，绘制饼图'
          // 人口统计_示例数据.xlsx
          // 统计20-29岁、30-39岁、40-49岁的女性人数占比，绘制饼图
          break
      }
      const formData = new FormData()
      const files = await this.getFile(file)
      formData.append('file', files)
      formData.append('metadata', JSON.stringify({ chatId: this.activeChatId }))
      formData.append('bucketName', 'chat')
      formData.append(
        'data',
        JSON.stringify({
          appId: this.appId,
          outLinkUid: this.outLinkUid,
          ...(this.shareId ? { shareId: this.shareId } : {})
        })
      )
      console.log(files)
      const res = await uploadFile(formData)
      if (res.code !== 200) {
        this.$message.warning({ message: res.msg, duration: 3000 })
        return
      }
      this.textarea = text
      this.fileList.push({
        name: file.split('/').pop(),
        url: res.data,
        response: res,
        raw: files
      })
      this.sendClick()
      this.isShowExcelBox = true
      this.excelbox = false
      setTimeout(() => {
        eventBus.$emit('refreshPoints')
      }, 200)
    },
    // 获得文件流
    async getFile(filePath) {
      try {
        // 1. 发起请求获取文件二进制数据
        const response = await fetch(filePath)
        if (!response.ok) {
          throw new Error(`文件获取失败: ${response.statusText}`)
        }

        // 2. 将响应转换为Blob
        const blob = await response.blob()

        // 3. 从路径中提取文件名
        const fileName = filePath.split('/').pop()
        console.log(blob)
        // 4. 创建File对象（包含文件名和MIME类型）
        return new File([blob], fileName, {
          type: blob.type || 'application/octet-stream'
        })
      } catch (error) {
        console.error('获取文件失败:', error)
        this.$message.error({ message: '文件获取失败，请检查路径是否正确', duration: 3000 })
        throw error // 抛出错误供调用方处理
      }
    },
    sendComExcelAssistClose() {
      this.isShowExcelBox = false
      this.isExcel = false
    },
    closeUpload() {
      // 如果有文件上传相关的弹窗需要关闭，在这里处理
      console.log('关闭上传相关弹窗')
    },
    handleCardClick(card) {
      console.log('Card clicked:', card)
      // 根据 card.value 执行相应操作
      this.textarea = card.value // 示例：将卡片的 value 值赋给 textarea
      this.sendClick() // 示例：发送消息
    },
    sendComFileUploadDialog(fileList) {
      this.fileList = [...fileList]
      console.log('change1')
      this.$nextTick(() => {
        this.$refs.fileUpload.submit()
      })
      this.isShowFunBox = false
    },
    sendComAssistance(row) {
      this.textarea = row.map((item) => item.text).join('')
      this.textList = [...row]
    },
    comAssistanceClose(falg) {
      if (this.textList.length > 0) {
        this.funTitle = '提示词帮助'
      } else {
        this.funTitle = ''
      }
    },
    sendComFileUploadClose() {
      this.funTitle = ''
      this.isShowFunBox = false
      this.showTranslateBar = false // 关闭上传弹窗时也隐藏翻译栏
      this.msgType = 'common'
    },
    editTitleClick(row) {
      console.log('row', row)
      this.curTitle = row
      this.$refs.comTitleEdit.show()
    },
    onProgress(event, file, fileList) {
      this.fileList = [...fileList]
    },
    // 添加文件选择后的处理逻辑
    handleFileChange(file) {
      if (this.imageFileExtensions.includes(file.raw.type.split('/')[1])) {
        const reader = new FileReader()
        reader.onload = (e) => {
          file.previewUrl = e.target.result
        }
        reader.readAsDataURL(file.raw)
      }
    },
    handleUploadSuccess(response, file, fileList) {
      console.log('response', response)
      if (response.code !== 200) {
        // this.$message.error(response.msg)
        this.$message.warning({ message: response.msg, duration: 3000 })
        return
      }
      this.fileList = fileList.map((item) => {
        return {
          ...item
        }
      })
      console.log('handleUploadSuccess', fileList)
    },
    triggerUpload() {
      this.$refs.fileUpload.$refs['upload-inner'].handleClick()
    },
    handleExceed(files, fileList) {
      this.$message.closeAll()
      this.$message.warning({
        message: `当前限制选择8个文件，本次选择了 ${files.length} 个文件，共选择了 ${fileList.length} 个文件`,
        duration: 3000
      })
    },
    handleUploadError(err, file, fileList) {
      // 检查是否是502错误
      if (err && err.status === 502) {
        this.$message.warning({ message: '服务器内部发生错误，请您刷新一下哦~', duration: 3000 })
      } else {
        const errStr = err.toString().replace(/^Error:\s*/, '')
        const errData = JSON.parse(errStr)
        // this.$message.error(errData.message)
        this.$message.closeAll()
        this.$message.warning({
          message: errData.msg ? errData.msg : errData.message,
          duration: 3000
        })
      }
      this.fileList = fileList
    },
    delFileClick(row, index) {
      this.fileList.splice(index, 1)
      if (this.fileList.length === 0) {
        this.textarea = ''
        this.textList = ''
        // 当删除所有文件后，重置funTitle状态，确保知识库类型重新显示
        this.funTitle = ''
      }
    },
    delAllClick() {
      this.$confirm('确认删除所有聊天记录？', '操作确认', {
        cancelButtonText: '取消',
        confirmButtonText: '确定'
      })
        .then(() => {
          clearHistories({
            appId: appId,
            shareId: localStorage.getItem('shareId'),
            outLinkUid: localStorage.getItem('outLinkUid')
          }).then((res) => {
            this.init()
          })
        })
        .catch(() => {})
    },
    toTopClick(row) {
      // todo
      const top = row.timeCategory !== 'top'
      updateHistory({ appId: appId, chatId: row.chatId, top }).then((res) => {
        this.init()
      })
    },
    delHistoryClick(row) {
      delHistory({
        appId: appId,
        chatId: row.chatId
      }).then((res) => {
        if (res.code !== 200) {
          // this.$message.error(res.data.msg)
          this.$message.closeAll()
          this.$message.warning({ message: res.data.msg, duration: 3000 })
        } else {
          if (this.curChat.chatId === row.chatId) {
            this.initAIMsg()
            this.isThinking = false
          }
          this.init()
        }
      })
    },
    async newChatClick(con) {
      console.log('newChatClick from', con)
      const newChat = this.historyList.find((item) => item.title === '新的会话')

      if (newChat) {
        if (newChat.chatId !== this.curChat.chatId) {
          // 只有当不是Excel助手模式时才重置状态
          // if (this.funTitle !== 'excel助手') {
          this.isShowExcelBox = false
          this.funTitle = ''
          this.textarea = ''
          this.isShowFunBox = false
          // }
          this.historyItemClick(newChat)
        } else {
          // this.$message.error('已是最新的对话')
          this.$message.closeAll()
          this.$message.warning({ message: '已是最新的对话', duration: 3000 })
        }
        return
      }
      // this.init()
      this.usedShortcutInCurSessionNumber = 0
      const chatId = generateChatId(24)
      this.curChat = {
        chatId: chatId,
        title: '新的会话',
        appId: appId,
        top: false,
        timeCategory: 'today'
      }
      console.log('newChatClick')
      this.historyList.unshift(this.curChat)
      this.initAIMsg()
      // 主动停止对话流
      this.stopFetchSource()
      // 只有当不是Excel助手模式时才重置Excel助手相关状态
      // if (this.funTitle !== 'excel助手') {
      this.isShowExcelBox = false
      this.isExcel = false
      this.excelbox = false
      // 这里是造成都变成common的原因，所以这里不能重置funTitle
      // this.funTitle = ''
      this.closeUpload()
      // }

      await this.historyItemClick(this.curChat)
    },
    sendComMsgLeft() {
      this.historyItemClick(this.curChat)
    },
    /** 选择历史会话列表 */
    handleHistoryItemClick(row) {
      if (row.chatId === this.curChat.chatId) {
        return
      }
      // 更新 excelbox 状态
      this.excelbox = false
      // 清空文本和文件
      this.clearInputAndFiles()
      this.initAIMsg()
      // 主动停止对话流
      this.stopFetchSource()
      if (this.funTitle !== 'excel助手' || this.curChat.chatId !== row.chatId) {
        this.isShowExcelBox = false
        this.funTitle = ''
      }
      this.historyItemClick(row)
      this.isShowExcelBox = false
      this.funTitle = ''
    },
    async historyItemClick(row) {
      // 1.关闭文件上传窗口
      this.closeUpload()
      // 2.初始化会话
      await chatInit1({ chatId: row.chatId, shareId: this.shareId }).then((res) => {})
      // 3.设置当前会话为选中的历史记录项
      this.curChat = row
      localStorage.setItem('curChat', JSON.stringify(this.curChat))
      await getPaginationRecords({
        offset: 0,
        pageSize: 10,
        // shareId: this.shareId,
        type: 'outLink',
        // appId: appId,
        // chatId: row.chatId
        appId: appId,
        chatId: row.chatId
      })
        .then((res) => {
          // 4.将Human文件名字通过前端的方式插入到AI对话中
          this.msgList = res.data.list.map((item, index, array) => {
            // 检查当前项是否为 AI 消息，且前一项是否为 Human 消息
            if (item.obj === 'AI' && index > 0 && array[index - 1].obj === 'Human') {
              const humanMsg = array[index - 1]

              // 检查 Human 消息中是否包含文件类型数据
              const fileData = humanMsg.value.find((val) => val.type === 'file')

              if (fileData) {
                // 创建新的 AI 消息对象，避免直接修改原对象
                const newItem = JSON.parse(JSON.stringify(item))

                // 将文件数据添加到 AI 消息的 value 数组中
                newItem.value.push(fileData)

                return newItem
              }
            }

            // 如果不需要修改，返回原始项
            return item
          })
          // console.log(res.data.list)
          // console.log(this.msgList)
          // 5.滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom('historyItemClick')
            // 更新底部状态
            this.isAtBottom = true
          })
          // 6.更新已使用的快捷方式数量
          this.addUsedShortcutInCurSessionNumber()
        })
        .catch((e) => {
          console.log('e', e)
        })
    },
    sendComMsgRight(msg) {
      this.submit()
    },
    sendComMsgRightDel(msg) {
      const index = this.msgList.findIndex((item) => item.dataId === msg.dataId)
      // 还要删除AI回复的那一条记录
      itemDelete({
        appId: appId,
        chatId: this.curChat.chatId,
        contentId: this.msgList[index + 1].dataId
      }).then(async(res) => {
        // 删除后重置思考状态
        this.isThinking = false
        if (this.msgList && this.msgList.length > 2) {
          await this.historyItemClick(this.curChat)
          await this.getChatHistoryList(1)
          const currentChat = this.historyList.find((item) => item.chatId === this.curChat.chatId)
          if (currentChat) {
            this.curChat = { ...currentChat }
            localStorage.setItem('curChat', JSON.stringify(currentChat))
          }
        } else {
          this.delHistoryClick(this.curChat)
        }
      })
    },
    // 重新发送实际上少了 参数biztype
    sendComMsgRightReSubmit(row) {
      console.log('row', row)
      const startIndex = this.msgList.findIndex((item) => item.dataId === row.dataId)
      this.delList = this.msgList.filter((_, index) => startIndex <= index)
      this.delList.map((item, index) => {
        itemDelete({
          appId: appId,
          chatId: this.curChat.chatId,
          contentId: item.dataId
        }).then((res) => {})
      })
      this.msgList = this.msgList.slice(0, startIndex)
      //   1.生成结构数据塞到msgList中
      const msg = new FileMsgClass(row.dataId, 'Human', '', '', row.value)
      console.log('msg', msg)
      this.msgList.push(msg)
      // 重新发起SSE请求
      setTimeout(() => {
        //   2.组装参数
        // 文字+文件
        const params = new FileCompletions(
          row.dataId,
          this.textarea,
          appId,
          this.curChat.chatId,
          // "shareChat-1741425008504-wybMmtwWk9k3KdEATvwOLRht",
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid'),
          '',
          row.value,
          this.translateFrom,
          this.translateTo
        )
        console.log('params', params)
        //         // 3.发起SSE
        this.sseReq(params)
      }, 2000)
    },

    // scrollToBottom(nub) {
    //   // if (type === 'btnClick') {
    //   //   this.autoScroll = true
    //   // }
    //   console.log('滑到底部' + nub)
    //   // 这里是Vue2的写法
    //   const box = this.$refs.contentEa
    //   // console.log('this.autoScroll', this.autoScroll)
    //   if (box) {
    //     // 使用setTimeout确保DOM已经更新
    //     setTimeout(() => {
    //       box.scrollTo({ top: box.scrollHeight, behavior: 'smooth' })
    //       // 滚动后更新状态
    //       this.isAtBottom = true
    //     }, 100)
    //   }
    // },
    scrollToBottom(nub) {
      // console.log('滑到底部index' + nub)
      const box = this.$refs.contentEa
      if (box) {
        // 只有在允许自动滚动且用户没有手动滚动时才滚动
        if (!this.userScrolling && this.autoScroll) {
          this.jsScrolling = true
          // 使用requestAnimationFrame确保在DOM更新后执行滚动
          requestAnimationFrame(() => {
            // 使用精确的滚动位置，确保包括点赞点踩按钮在内的所有内容都可见
            const targetScrollTop = box.scrollHeight - box.clientHeight
            box.scrollTop = targetScrollTop

            // 添加保障性延时滚动，确保内容完全渲染后再滚动
            setTimeout(() => {
              if (!this.userScrolling) {
                const newTargetScrollTop = box.scrollHeight - box.clientHeight
                box.scrollTop = newTargetScrollTop
              }
              this.jsScrolling = false
            }, 50)

            // 对于可能的异步内容更新，添加额外的滚动保障
            setTimeout(() => {
              if (!this.userScrolling && this.autoScroll) {
                const finalTargetScrollTop = box.scrollHeight - box.clientHeight
                box.scrollTop = finalTargetScrollTop
              }
            }, 150)
          })
        }
      }
    },
    forceScrollToBottom() {
      this.userScrolling = false
      this.jsScrolling = true
      this.showBackToBottom = false

      const box = this.$refs.contentEa
      if (box) {
        requestAnimationFrame(() => {
          // 使用精确的滚动位置
          const targetScrollTop = box.scrollHeight - box.clientHeight
          box.scrollTop = targetScrollTop

          // 添加保障性延时滚动
          setTimeout(() => {
            const newTargetScrollTop = box.scrollHeight - box.clientHeight
            box.scrollTop = newTargetScrollTop
            this.jsScrolling = false
          }, 50)
        })
      }
    },
    // 如果这些方法不存在，则添加它们
    handleContentUpdated() {
      // 内容更新时触发滚动
      this.scrollToBottom('contentUpdated')
    },

    handleContentRenderComplete() {
      // 内容渲染完成时触发滚动
      this.scrollToBottom('contentRenderComplete')
      // 渲染完成后重置滚动状态
      this.userScrolling = false
    },

    handleFirstCharacterRendered(data) {
      // 保持原有的处理逻辑
      // console.log('第一个字符已渲染，时间:', data.timestamp)
      // 可以在这里添加其他需要的处理逻辑
    },

    handleTypingAnimationStarted(type, timestamp) {
      // 保持原有的处理逻辑
      // console.log('打字机动画开始:', type, timestamp)
      // 可以在这里添加其他需要的处理逻辑
    },
    renderVoice(text) {
      const msg = new SpeechSynthesisUtterance(text)
      const voices = window?.speechSynthesis?.getVoices?.() || [] // 获取语言包
      const voice = voices.find((item) => {
        return item.lang === 'zh-CN'
      })
      msg.rate = 1
      if (voice) {
        msg.onstart = () => {}
        msg.onend = () => {
          msg.onstart = null
          msg.onend = null
        }
        msg.voice = voice
      }
      return msg
    },
    productSegments(text) {
      if (this.speechList.length) {
        text += this.speechList.shift()
        const reg = /[,;!?，。；！？]/
        if (reg.test(text)) {
          return text
        } else {
          return this.productSegments(text)
        }
      } else {
        return text
      }
    },
    transAudioSuccess(data) {
      this.textarea = data
      this.textList = ''
    },
    handleScroll(e) {
      const { scrollTop, clientHeight, scrollHeight } = e.target
      const threshold = 50 // 距离底部的阈值
      // 判断是否接近底部
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - threshold
      // 更新 isAtBottom 状态
      this.isAtBottom = isAtBottom
      // 如果不在底部且不是JS正在滚动，则标记为用户手动滚动
      if (!isAtBottom && !this.jsScrolling) {
        this.userScrolling = true
        this.showBackToBottom = true
      } else if (isAtBottom) {
        // 如果在底部，允许自动滚动
        this.userScrolling = false
        this.showBackToBottom = false
      }
    },
    popoverChange() {
      this.$refs.popover.showPopper = false
    },
    getLabel(item, list) {
      const index = list.findIndex((o) => o.value === item)
      if (index !== -1) {
        return list[index].label
      }
    },
    handleUserCommand(command) {
      if (command === 'getPoints') {
        this.getPoints()
      } else if (command === 'pointsRank') {
        this.showPointsRank()
      } else if (command === 'myfeedback') {
        this.showMyFeedback()
      }
    },

    getPoints() {
      // 获取积分的逻辑
      this.$refs.pointRule.show()
    },

    showPointsRank() {
      // 显示积分排行的逻辑
      this.$refs.pointRank.show()
    },

    showMyFeedback() {
      this.$refs.myFeedback.show()
    },
    // 粘贴图片和文件
    handlePaste(e) {
      // console.log('粘贴', e)
      const clipboardData = e.clipboardData || window.clipboardData
      if (!clipboardData) return
      const items = clipboardData.items
      if (!items) return
      let hasFiles = false
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.kind === 'file') {
          const file = item.getAsFile()
          if (file && this.beforeAvatarUpload(file)) {
            hasFiles = true
            // 使用el-upload组件的handleStart方法添加文件
            this.$refs.fileUpload.handleStart(file)
          }
        }
      }
      // 如果有文件被添加，调用submit方法上传
      if (hasFiles) {
        this.$nextTick(() => {
          this.$refs.fileUpload.submit()
        })
      }
    },
    // 清空输入框和文件列表
    clearInputAndFiles() {
      this.funTitle = ''
      this.isShowFunBox = false
      this.textarea = ''
      this.textList = ''
      this.fileList = []
      // 如果有上传组件的引用，重置它
      if (this.$refs.fileUpload && typeof this.$refs.fileUpload.clearFiles === 'function') {
        this.$refs.fileUpload.clearFiles()
      }
    },
    // handleDocument() {
    //   this.textarea = `中英互译:${
    //     this.translateFrom == '自动检测' ? '自动检测语言' : this.translateFrom
    //   }-${this.translateTo}`
    // },
    checkAllTitleOverflow() {
      // 遍历所有历史会话
      if (!this.historyTypeList) return
      this.historyTypeList.forEach((item) => {
        item.children.forEach((v) => {
          // 这里用唯一ref名
          const refName = 'titleRef_' + v.chatId
          const ref = this.$refs[refName]
          // 兼容 v-for ref 可能为数组
          const el = Array.isArray(ref) ? ref[0] : ref
          if (el && el.scrollWidth && el.offsetWidth) {
            this.$set(this.titleOverflowMap, v.chatId, el.scrollWidth > el.offsetWidth)
          } else {
            this.$set(this.titleOverflowMap, v.chatId, false)
          }
        })
      })
    },
    isTitleOverflow(v) {
      return !!this.titleOverflowMap[v.chatId]
    },
    excel1() {
      this.isShowExcelBox = false
    },
    checkIfAtBottom() {
      const box = this.$refs.contentEa
      if (box) {
        const { scrollTop, clientHeight, scrollHeight } = box
        // 如果已经接近底部，则标记为在底部
        if (scrollTop + clientHeight >= scrollHeight - 60) {
          this.isAtBottom = true
        }
      }
    },
    sendComMsgRightEdit(text) {
      this.textarea = text
    }
  }
}
</script>

<style lang="scss">
.deeepseek-index {
  & .indexPage {
    width: 100%;
    height: 100vh;
    .mask-left {
      z-index: 5;
      pointer-events: none;
      background: linear-gradient(rgba(249, 251, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
      // background-color: red;
      width: 97%;
      height: 32px;
      position: absolute;
      top: 70px;
      left: 1px;
    }
    .mask-left-b {
      z-index: 5;
      pointer-events: none;
      background: linear-gradient(to top, rgba(249, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
      // background-color: red;
      width: 97%;
      height: 32px;
      position: absolute;
      bottom: 256px;
      left: 1px;
    }
    .inner {
      background-color: white;
      height: 100%;

      .leftSide {
        height: calc(100vh);
        position: relative;
        background: #f9fbff;
        display: flex;
        flex-direction: column;
        .el-menu-vertical-container {
          flex: 1 !important;
          height: auto !important; /* 让高度自动适应内容 */
          max-height: calc(100% - 196px) !important; /* 减去底部区域的高度 */
        }
      }

      .collapse {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      .rightEa {
        flex: 1;
        overflow: auto;
        align-self: flex-start;
        position: relative;
        flex-direction: column;
        height: calc(100vh);
      }
    }

    .border-radius-20 {
      border-radius: 20px;
      overflow: hidden;
    }

    .msgItem {
      cursor: pointer;
      border-radius: 20px;
      font-weight: 400;
      font-size: 16px;
      color: #262626;
      padding: 10px;
      &-active {
        background-color: var(--color-primary-light7);
      }
      &:hover {
        background-color: var(--color-primary-light9);
      }
    }

    .el-textarea__inner {
      background-color: #f7fbf6 !important;
      border: none;
      padding: 12px 16px;
      line-height: 28px;
      height: 130px !important;
      min-height: 130px !important;
      max-height: 250px;
    }

    /* 添加过渡效果 */
    .slide-fade-enter-active,
    .slide-fade-leave-active {
      transition: all 0.5s ease;
    }

    .slide-fade-enter,
    .slide-fade-leave-to {
      transform: translateX(-260px);
      opacity: 0;
    }

    .logoImg {
      height: 30px;
    }

    .content {
      width: 100%;
      overflow-y: auto;
      flex: 1;
      padding: 20px 120px;
      .mask {
        z-index: 5;
        pointer-events: none;
        background: linear-gradient(rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
        // background-color: red;
        width: 97%;
        height: 32px;
        position: absolute;
        top: 32px;
        left: 1px;
      }
      .back-to-bottom-btn {
        position: absolute;
        bottom: 270px;
        left: 50%;
        transform: translate(-50%, 0%);
        z-index: 999;
      }
    }

    .inputEa {
      position: relative;
      padding: 20px 120px;
      .input-container {
        background: #f7fbf6;
        width: 100%;
        position: relative;
        border-radius: 10px;
        // box-shadow: 0px 4px 4px 0px #dcdddf;
        box-shadow: 0px 4px 10px 0px rgba(65, 77, 96, 0.06);
        border: 1px solid #e5e5e5;
        &.inputActive {
          border: 1px solid #4cb848;
        }
      }

      .fileEa {
        overflow: auto;
        background-color: #f7fbf6;
        border-radius: 10px 10px 0 0;
        &.borderBox {
          border: 1px solid #e5e5e5;
          border-bottom: none;
        }
        .imgType {
          position: relative;
          width: 60px;
          height: 60px;
          //margin-right: 10px;
          img {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            overflow: hidden;
          }

          &:hover {
            .closeBtn {
              cursor: pointer;
              display: block;
            }
          }
        }

        .fileType {
          position: relative;

          &:hover {
            .closeBtn {
              cursor: pointer;
              display: block;
            }
          }

          img {
            width: 40px;
            height: 40px;
          }

          .text {
            font-size: 14px;
            color: #97999b;
          }
        }

        .closeBtn {
          position: absolute;
          top: -8px;
          right: -8px;
          display: none;

          &:hover {
            color: red;
          }
        }
      }

      .textarea {
        position: relative;
        box-sizing: border-box;
        border: 1px solid transparent;
        background-color: #f7fbf6;
        padding: 2px;
        border-radius: 10px;
        // min-width: 800px;
      }
      // .inputActive {
      //   border: 1px solid #4cb848;
      //   border-top: none;
      //   border-radius: 0 0 10px 10px;
      //   position: relative;
      //   box-sizing: border-box;
      //   padding: 2px;
      //   background-color: #f7fbf6;
      // }

      .buttonEa {
        position: absolute;
        right: 10px;
        bottom: 10px;

        .sendBtn {
          padding: 5px;
        }
      }
    }

    .clearAll {
      &:hover {
        border-color: #e26158;
        color: #e26158;
      }
    }

    .icon {
      width: 20px;
      overflow: hidden;
      height: 20px;
      vertical-align: middle;

      svg {
        fill: #00afff;
      }
    }

    .arrowEa {
      cursor: pointer;
      color: white;
      width: 16px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #7f7f7f;
      border-radius: 0 10px 10px 0;
      padding: 0;
      margin: 0;
    }

    .progress {
      background-color: rgba(0, 0, 0, 0.5);
      position: absolute;
      left: 0;
      top: 0;
    }

    .addBtn {
      cursor: pointer;
      // margin-left: 10px;
      // width: 50px;
      padding: 0 13px;
      height: 50px;
      font-size: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .msg-config-line {
      display: flex;
      flex-direction: row;
      justify-content: end;
      align-items: center;
      height: 74px;

      .model-switch {
        flex-grow: 1;
        height: 44px;
        display: flex;
        flex-direction: row;
        align-items: center;

        .btn {
          width: 140px;
          height: 30px;
          text-align: center;
          background-color: #ebf5e8;
          line-height: 30px;
          color: #4cb848;
          border-radius: 15px;
        }
      }

      .upload-file,
      .spic-file,
      .sent-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 44px;
        width: 44px;
        font-size: 26px;
        line-height: 44px;
      }
    }
    .modelSelEa {
      // background-color: #f7fbf6;
      left: 0;
      bottom: 0;
      padding: 0 10px 6px 10px;
      width: 100%;
    }
  }
  & .flex-text {
    display: inline-flex;
    align-items: center;
  }
  & .sp-deepSeek {
    font-size: 60px;
    margin-right: 8px;
  }
  & .flex-container {
    display: flex;
  }
  & .chat-btn {
    border: none;
    .chat1 {
      font-size: 22px;
      margin-right: 6px;
    }
    .chat2 {
      width: 36px;
      height: 20px;
    }
    span {
      font-size: 16px;
      font-weight: 600;
    }
  }
  & .expand-top {
    color: #8b8b8b;
    font-size: 26px;
  }
  & .expand-top:hover {
    color: var(--color-primary);
  }
  & .collapse-icon {
    font-size: 24px;
    margin-top: 20px;
    color: #8b8b8b;
  }
  & .collapse-icon1 {
    font-size: 24px;
    margin-top: 20px;
    color: #8b8b8b;
  }
  & .collapse-icon2 {
    font-size: 36px;
    bottom: 0px;
    color: #8b8b8b;
  }
  & .collapse-expand {
    transform: rotate(180deg);
  }
  & .series-bottom {
    width: 100%;
    padding: 0px;
    height: 196px;
  }
  & .series-bottom1 {
    height: 196px;
  }
  & .collapse-service {
    color: var(--color-primary);
    font-size: 20px;
    margin-right: 6px;
  }
  & .chat-list-container {
    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
      width: 0;
      height: 0;
    }
    height: calc(100% - 98px);
    overflow-y: auto;
  }
  & .chat-title {
    font-weight: 700;
    font-size: 14px;
    color: #555;
    margin: 20px 0 10px 0;
  }
  & .chat-content-top {
    width: 100%;
    font-family: Microsoft YaHei UI;
    font-weight: 700;
    font-size: 16px;
    color: #262626;
  }

  & .active-btn {
    color: var(--color-primary);
    // font-size: 32px;
  }
  & .disabled-btn {
    cursor: not-allowed;
    color: #c9d5e3;
    // font-size: 32px;
  }
  & .upload-icon {
    color: #c9d5e3;
    font-size: 24px;
  }
  & .mr-16 {
    margin-right: 16px;
  }
  textarea::-webkit-resizer {
    display: none; /* 隐藏右下角调整大小把手 */
  }
  .no-history-list {
    justify-content: center;
  }
  .deep-input {
    color: #262626;
    font-weight: 400;
    font-size: 16px;
  }
}
</style>
<style lang="scss" scoped>
.top-btn {
  color: #464646;
  width: 100%;
  text-align: left;
}
.chat-item-dropdown {
  display: none;
  width: 22px;
  text-align: center;
}
.chat-list-item {
  margin-top: 6px;
}
.chat-list-item:hover .chat-item-dropdown {
  display: inline-block;
}
.chat-item-title {
  display: inline-block;
  flex: 1;
}
.chat-item-content {
  display: inline-block;
  max-width: 100%;
  font-size: 16px;
}
.base-textarea {
  min-height: 130px;
  max-height: 250px;
  overflow: auto;
}
.stopReadBtn {
  margin-right: 8px;
}
.send-btns {
  font-size: 22px;
}

/*鼠标点击后移开，恢复本身样式*/
.chat-btn:focus:not(.el-button:hover) {
  // border-color: var(--el-button-border-color);
  // background-color: var(--el-button-bg-color);
  color: #4cb848;
  background-color: #edf8ed;
}

.mask-b1 {
  z-index: 5;
  pointer-events: none;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
  // background-color: red;
  width: 97%;
  height: 32px;
  position: absolute;
  top: -32px;
  left: 1px;
}

.service-info .el-divider {
  margin: 0px;
  padding: 0px;
  background-color: #efefef;
}

.service-content {
  padding: 0px 15px 0 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-info-box {
  display: flex;
  align-items: center;
  padding: 0px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  flex-shrink: 0;
  // border: 1px solid #eee;
  background-color: #f5f7fa;
  .user-avatar-img {
    height: 50px;
    background: url('../assets/images/userAvatar.png') no-repeat center center;
    background-size: cover;
  }
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info-text {
  flex: 1;
  overflow: hidden;
  line-height: 1.5;
}

.user-info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 5px;
}

.user-info-row:last-child {
  margin-bottom: 0;
}

.user-info-label {
  font-size: 16px;
  color: #343435;
  white-space: nowrap;
}
.user-info-label1 {
  font-size: 16px;
  color: #4cb848;
  white-space: nowrap;
}

.user-info-value {
  font-size: 16px;
  color: #343435;
  white-space: nowrap;
}

.points-value {
  color: #4cb848;
  font-weight: 500;
}

/* 用户下拉菜单样式 */
.user-dropdown-menu {
  padding: 0 !important;
  border: 1px solid #67c23a !important;
  border-radius: 8px !important;
  overflow: hidden;
  min-width: 246px !important;
}

.user-dropdown-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fff;
}

.user-avatar-large {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.user-dropdown-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.user-dropdown-item {
  display: flex !important;
  align-items: center;
  padding: 12px 15px !important;
  height: auto !important;
  line-height: normal !important;
  font-size: 16px;
}

.dropdown-icon {
  font-size: 20px;
  margin-right: 20px;
}

.green-icon {
  color: #67c23a;
}
.el-divider--horizontal {
  margin: 0 !important;
}
/* 底部服务热线样式 */
.service-hotline-footer {
  position: fixed;
  bottom: 0;
  right: 20px;
  color: #343434;
  padding: 26px 60px;
  border-radius: 4px 4px 0 0;
  font-size: 14px;
  font: Microsoft YaHei UI;
  z-index: 100;
  justify-content: center;
  align-items: center;
}

.service-hotline-footer span {
  display: inline-block;
}
.service-icon {
  width: 28px;
  height: 28px;
  margin-right: 4px;
  color: var(--color-primary);
}
/* 底部AI生成内容提示样式 - 带偏移量 */
.bottom-tips {
  position: fixed;
  left: 50%;
  right: 0;
  text-align: center;
  color: #a3a3a3;
}
/* 深度选择器 - Vue 3 */
::v-deep .custom-select .el-input__inner {
  border: none !important;
  box-shadow: 0px 4px 4px 0px #e1e1e1 !important;
}
.excel-assist-tag {
  margin-left: 12px;
  .excel-icon {
    margin-right: 9px;
    font-size: 16px;
    color: #059669;
  }
}
.span.excel-text {
  font-size: 16px;
}
.excel-img {
  padding: 14px 20px 0px 20px;
  //屏幕高度小于740px
  @media screen and (max-height: 740px) {
    height: 80px;
  }
}
.el-card {
  background-color: #f2f2f2;
}
.card-desc-overlay {
  min-height: 65px;
  margin-bottom: 6px;
  margin-top: -10px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(2px);
  padding: 10px 14px;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  border-radius: 5px;
  border: none;
  @media screen and (min-width: 1170px) {
    height: 80px;
  }
  @media screen and (max-width: 1171px) {
    height: 100px;
  }
  @media screen and (max-width: 1421px) {
    font-size: 14px;
    // min-height:88px;
  }
  //屏幕高度小于740px
  @media screen and (max-height: 740px) {
    font-size: 13px;
    min-height: auto;
  }
}
::v-deep .el-card__body {
  padding: 15px 20px 0px 20px;
}
.excel {
  // min-width: 800px;
}
.scroll-to-bottom-container {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  margin-bottom: 10px;
  width: 44px;
  min-height: 44px;
}

.scroll-to-bottom-btn {
  width: 44px;
  height: 44px;
  min-width: 44px;
  min-height: 44px;
  padding: 0;
  border: 1px solid #dcdfe6;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s;
  position: absolute;
  bottom: 18px;
}
.scroll-to-bottom-btn:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); /* 鼠标悬停时显示阴影 */
}
</style>
